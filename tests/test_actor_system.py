"""
Tests for the Actor system and individual actors.

This module tests the Actor base class, individual actors,
and the ActorSystem manager to ensure proper functionality.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch

from app.utils.actor import Actor, ActorState
from app.utils.actor_system import ActorSystem, ActorSystemState
from app.utils.event_bus import event_bus


class MockActor(Actor):
    """Mock actor for testing."""
    
    def __init__(self, name=None, should_fail_init=False, should_fail_start=False):
        super().__init__(name)
        self.should_fail_init = should_fail_init
        self.should_fail_start = should_fail_start
        self.initialized = False
        self.started = False
        self.stopped = False
    
    async def initialize(self):
        if self.should_fail_init:
            raise RuntimeError("Mock initialization failure")
        self.initialized = True
    
    async def start_actor(self):
        if self.should_fail_start:
            raise RuntimeError("Mock start failure")
        self.started = True
    
    async def stop_actor(self):
        self.stopped = True


class TestActor:
    """Test the Actor base class."""
    
    @pytest.fixture
    def mock_actor(self):
        return MockActor("test_actor")
    
    def test_actor_creation(self, mock_actor):
        """Test actor creation and initial state."""
        assert mock_actor.name == "test_actor"
        assert mock_actor.state == ActorState.CREATED
        assert not mock_actor.is_running()
        assert not mock_actor.is_stopped()
        assert not mock_actor.has_error()
    
    async def test_actor_lifecycle_success(self, mock_actor):
        """Test successful actor lifecycle."""
        # Start the actor
        await mock_actor.start()
        
        assert mock_actor.state == ActorState.RUNNING
        assert mock_actor.initialized
        assert mock_actor.started
        assert mock_actor.is_running()
        assert not mock_actor.has_error()
        
        # Stop the actor
        await mock_actor.stop()
        
        assert mock_actor.state == ActorState.STOPPED
        assert mock_actor.stopped
        assert mock_actor.is_stopped()
        assert not mock_actor.is_running()
    
    async def test_actor_initialization_failure(self):
        """Test actor initialization failure."""
        actor = MockActor("failing_actor", should_fail_init=True)
        
        with pytest.raises(RuntimeError, match="Mock initialization failure"):
            await actor.start()
        
        assert actor.state == ActorState.ERROR
        assert actor.has_error()
        assert not actor.is_running()
    
    async def test_actor_start_failure(self):
        """Test actor start failure."""
        actor = MockActor("failing_actor", should_fail_start=True)
        
        with pytest.raises(RuntimeError, match="Mock start failure"):
            await actor.start()
        
        assert actor.state == ActorState.ERROR
        assert actor.has_error()
        assert actor.initialized  # Should have initialized successfully
        assert not actor.started
    
    async def test_actor_double_start(self, mock_actor):
        """Test that starting an already started actor raises an error."""
        await mock_actor.start()
        
        with pytest.raises(RuntimeError, match="cannot be started from state"):
            await mock_actor.start()
    
    def test_actor_status(self, mock_actor):
        """Test actor status reporting."""
        status = mock_actor.get_status()
        
        assert status["name"] == "test_actor"
        assert status["state"] == ActorState.CREATED.value
        assert status["uptime"] is None
        assert not status["has_error"]
    
    async def test_actor_uptime(self, mock_actor):
        """Test actor uptime calculation."""
        await mock_actor.start()
        
        # Wait a bit to get some uptime
        await asyncio.sleep(0.1)
        
        uptime = mock_actor.get_uptime()
        assert uptime is not None
        assert uptime > 0
        
        await mock_actor.stop()
        
        # Uptime should still be available after stopping
        final_uptime = mock_actor.get_uptime()
        assert final_uptime is not None
        assert final_uptime >= uptime


class TestActorSystem:
    """Test the ActorSystem manager."""
    
    @pytest.fixture
    def actor_system(self):
        """Create a test actor system with mock actors."""
        system = ActorSystem()
        
        # Replace the real actors with mock actors for testing
        system.actors = {
            "mock_actor_1": MockActor("mock_actor_1"),
            "mock_actor_2": MockActor("mock_actor_2"),
            "mock_actor_3": MockActor("mock_actor_3")
        }
        
        system.startup_order = ["mock_actor_1", "mock_actor_2", "mock_actor_3"]
        system.shutdown_order = ["mock_actor_3", "mock_actor_2", "mock_actor_1"]
        
        return system
    
    def test_actor_system_creation(self, actor_system):
        """Test actor system creation."""
        assert actor_system.state == ActorSystemState.CREATED
        assert len(actor_system.actors) == 3
        assert not actor_system.is_running()
        assert not actor_system.is_healthy()
    
    async def test_actor_system_startup_success(self, actor_system):
        """Test successful actor system startup."""
        # Mock the _wait_for_shutdown method to avoid hanging
        with patch.object(actor_system, '_wait_for_shutdown', new_callable=AsyncMock):
            await actor_system.start()
        
        assert actor_system.state == ActorSystemState.RUNNING
        
        # Check that all actors were started in order
        for actor in actor_system.actors.values():
            assert actor.is_running()
            assert actor.initialized
            assert actor.started
    
    async def test_actor_system_startup_failure(self, actor_system):
        """Test actor system startup with actor failure."""
        # Make one actor fail during initialization
        actor_system.actors["mock_actor_2"].should_fail_init = True
        
        with pytest.raises(RuntimeError, match="Failed to start actor mock_actor_2"):
            await actor_system.start()
        
        assert actor_system.state == ActorSystemState.ERROR
    
    async def test_actor_system_shutdown(self, actor_system):
        """Test actor system shutdown."""
        # Start the system first
        with patch.object(actor_system, '_wait_for_shutdown', new_callable=AsyncMock):
            await actor_system.start()
        
        # Now test shutdown
        await actor_system.stop()
        
        assert actor_system.state == ActorSystemState.STOPPED
        
        # Check that all actors were stopped
        for actor in actor_system.actors.values():
            assert actor.is_stopped()
            assert actor.stopped
    
    def test_actor_system_status(self, actor_system):
        """Test actor system status reporting."""
        status = actor_system.get_system_status()
        
        assert status["system_state"] == ActorSystemState.CREATED.value
        assert status["total_actors"] == 3
        assert status["running_actors"] == 0
        assert status["failed_actors"] == 0
        assert "actors" in status
    
    def test_actor_system_metrics(self, actor_system):
        """Test actor system metrics."""
        metrics = actor_system.get_system_metrics()
        
        assert metrics["total_actors"] == 3
        assert metrics["running_actors"] == 0
        assert metrics["stopped_actors"] == 0
        assert metrics["error_actors"] == 0
        assert metrics["total_uptime"] == 0.0
    
    def test_get_actor(self, actor_system):
        """Test getting individual actors."""
        actor = actor_system.get_actor("mock_actor_1")
        assert actor is not None
        assert actor.name == "mock_actor_1"
        
        non_existent = actor_system.get_actor("non_existent")
        assert non_existent is None


class TestActorEventBusIntegration:
    """Test Actor integration with the event bus."""
    
    class EventTestActor(Actor):
        """Test actor with event handlers."""
        
        def __init__(self):
            super().__init__("event_test_actor")
            self.received_events = []
        
        async def initialize(self):
            pass
        
        async def start_actor(self):
            pass
        
        async def stop_actor(self):
            pass
        
        @event_bus.subscribe("test_event")
        async def handle_test_event(self, event_data):
            self.received_events.append(event_data)
    
    async def test_actor_event_subscription(self):
        """Test that actors can subscribe to events."""
        actor = self.EventTestActor()
        
        # Start the actor (this should subscribe to events)
        await actor.start()
        
        # Publish a test event
        await event_bus.publish("test_event", {"message": "test"})
        
        # Give the event bus time to process
        await asyncio.sleep(0.1)
        
        # Check that the actor received the event
        assert len(actor.received_events) == 1
        assert actor.received_events[0]["message"] == "test"
        
        # Stop the actor (this should unsubscribe from events)
        await actor.stop()
        
        # Publish another event
        await event_bus.publish("test_event", {"message": "test2"})
        await asyncio.sleep(0.1)
        
        # Actor should not have received the second event
        assert len(actor.received_events) == 1


@pytest.mark.asyncio
class TestActorSystemIntegration:
    """Integration tests for the complete actor system."""
    
    async def test_full_system_lifecycle(self):
        """Test the complete system lifecycle with real actors."""
        # This test would use the real actor system
        # For now, we'll skip it to avoid dependencies on external services
        pytest.skip("Integration test requires external dependencies")
    
    async def test_actor_communication(self):
        """Test communication between actors through events."""
        # This would test the full message flow between actors
        pytest.skip("Integration test requires full system setup")
